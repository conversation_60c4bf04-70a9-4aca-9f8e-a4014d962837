package amzn

import (
	"contentmanager/etc/conf"
	"contentmanager/logging"
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/cloudfront"
	uuid "github.com/satori/go.uuid"
	"regexp"
	"slices"
	"strings"
	"time"
)

var (
	cf                  = cloudfront.New(conf.AwsS3Session)
	queuedInvalidations = NewInvalidationMap()
)

// AbsPathRateLimit Cloudfront Rate Limiting: https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/InvalidationLimits.html
const AbsPathRateLimit = 3000

func QueueInvalidations(distributions []string, paths []string) {
	for _, distID := range distributions {
		queuedInvalidations.Add(distID, paths)
	}
}

func ProcessQueuedInvalidations() {
	drained := queuedInvalidations.Drain()
	logging.RootLogger().Info().Msgf("[ProcessQueuedInvalidations] Processing %d invalidations", len(drained))

	for distributionID, paths := range drained {
		if len(paths) == 0 {
			continue
		}
		batch := sortAndFilter(paths, AbsPathRateLimit)
		if _, err := Invalidate(distributionID, batch); err != nil {
			//   - ErrCodeBatchTooLarge "BatchTooLarge"
			//     Invalidation batch specified is too large.
			//
			//   - ErrCodeTooManyInvalidationsInProgress "TooManyInvalidationsInProgress"
			//     You have exceeded the maximum number of allowable InProgress invalidation
			//     batch requests, or invalidation objects.
			logging.RootLogger().Err(err).
				Str("distribution_id", distributionID).
				Int("size", len(batch)).
				Msg("failed to create cloudfront invalidations")
		}
	}
}

func StartRequestInvalidator(ctx context.Context, interval time.Duration) {
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				logging.RootLogger().Info().Msg("StartRequestInvalidator routine shutting down gracefully")
				return
			case <-ticker.C:
				ProcessQueuedInvalidations()
			}
		}
	}()
}

func CreateInvalidationForResizer(paths []string) (interface{}, error) {
	return Invalidate("E2VL9ZGR8A829X", paths)
}

func Invalidate(distributionID string, paths []string) (interface{}, error) {
	// Prevent invalidation of paths that start with "*" or "/*"
	// (a Homepage invalidation will cause the whole site to be invalidated)
	validPaths := make([]string, 0, len(paths))
	for _, path := range paths {
		if strings.HasPrefix(path, "*") || strings.HasPrefix(path, "/*") {
			validPaths = append(validPaths, "/")
		} else {
			validPaths = append(validPaths, path)
		}
	}

	input := &cloudfront.CreateInvalidationInput{
		DistributionId: aws.String(distributionID),
		InvalidationBatch: &cloudfront.InvalidationBatch{
			CallerReference: aws.String(fmt.Sprintf("%d-%s", time.Now().Unix(), uuid.NewV4().String())), // Use timestamp as caller reference
			Paths: &cloudfront.Paths{
				Quantity: aws.Int64(int64(len(validPaths))),
				Items:    aws.StringSlice(validPaths),
			},
		},
	}

	result, err := cf.CreateInvalidation(input)
	return result, err
}

var (
	reg = regexp.MustCompile("^(/images|/documents|/thumbnails|/folder)")
)

func sortAndFilter(paths []string, size int) []string {
	if len(paths) <= size {
		return paths
	}
	slices.SortFunc(paths, func(a, b string) int {
		aMatch := reg.MatchString(a)
		bMatch := reg.MatchString(b)
		if aMatch && !bMatch {
			return -1
		}
		if !aMatch && bMatch {
			return 1
		}
		return 0
	})
	return paths[:size]
}
